import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Chip,
  Button,
  List,
  Divider,
  Backdrop,
  CircularProgress,
  Badge
} from '@mui/material';
import {
  Work,
  KeyboardArrowUp,
  KeyboardArrowDown
} from '@mui/icons-material';
import JobItem from './JobItem';
import { JobData } from './JobsPanel';

interface JobsTrayProps {
  // Panel state
  isCollapsed: boolean;
  onToggleCollapse: () => void;

  // Jobs data
  jobs: JobData[];
  onClearJob: (jobId: string) => void;
  onClearAllJobs: () => void;

  // Media items for preview images
  mediaItems: Array<{
    id: string;
    filename: string;
    mimeType: string;
    baseUrl: string;
  }>;

  // Token for API calls
  token: string | null;
}

const JobsTray: React.FC<JobsTrayProps> = ({
  isCollapsed,
  onToggleCollapse,
  jobs,
  onClearJob,
  onClearAllJobs,
  mediaItems,
  token
}) => {
  const [lastJobCount, setLastJobCount] = useState(0);
  
  // Count active jobs for status display
  const activeJobs = jobs.filter(job => !['completed', 'failed', 'cancelled'].includes(job.status.toLowerCase()));
  const hasActiveJobs = activeJobs.length > 0;
  
  // Update last job count when panel is expanded
  useEffect(() => {
    if (!isCollapsed) {
      setLastJobCount(jobs.length);
    }
  }, [isCollapsed, jobs.length]);

  // Calculate new completed jobs for badge
  const newCompletedCount = Math.max(0, jobs.length - lastJobCount);

  // Sort jobs by creation date (newest first)
  const sortedJobs = [...jobs].sort((a, b) => {
    const dateA = new Date(a.createdAt || 0).getTime();
    const dateB = new Date(b.createdAt || 0).getTime();
    return dateB - dateA;
  });

  // Helper function to get media item for a job
  const getMediaItemForJob = (job: JobData) => {
    return mediaItems.find(item => item.id === job.mediaItemId);
  };

  // Tray dimensions
  const expandedHeight = { xs: '70vh', sm: '60vh', md: '400px' };
  const trayWidth = { xs: '100%', md: '380px' };
  const minimizedHeight = 56;

  // Don't render if no jobs
  if (jobs.length === 0) {
    return null;
  }

  return (
    <>
      {/* Mobile Backdrop */}
      <Backdrop
        open={!isCollapsed}
        onClick={onToggleCollapse}
        sx={{
          display: { xs: 'block', md: 'none' },
          zIndex: 1200,
          backgroundColor: 'rgba(0, 0, 0, 0.3)'
        }}
      />

      {/* Jobs Tray */}
      <Paper
        elevation={8}
        sx={{
          position: 'fixed',
          bottom: { xs: 0, md: 20 },
          right: { xs: 0, md: 20 },
          left: { xs: 0, md: 'auto' },
          width: trayWidth,
          zIndex: 1300,
          backgroundColor: 'background.paper',
          borderRadius: 1,
          transform: isCollapsed
            ? `translateY(calc(100% - ${minimizedHeight}px))`
            : 'translateY(0)',
          transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          height: isCollapsed ? minimizedHeight : expandedHeight,
          maxHeight: isCollapsed ? minimizedHeight : expandedHeight,
          overflow: 'hidden',
          boxShadow: '0 -4px 20px rgba(0,0,0,0.15)',
          border: '1px solid',
          borderColor: 'divider',
          borderBottom: { xs: 'none', md: '1px solid' }
        }}
      >
        {/* Minimized Tab */}
        {isCollapsed && (
          <Box
            onClick={onToggleCollapse}
            sx={{
              height: minimizedHeight,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              px: { xs: 3, md: 2 },
              cursor: 'pointer',
              '&:hover': {
                backgroundColor: 'action.hover'
              }
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 2, md: 1.5 } }}>
              <Badge
                badgeContent={newCompletedCount > 0 ? newCompletedCount : null}
                color="success"
                sx={{
                  '& .MuiBadge-badge': {
                    fontSize: '0.7rem',
                    height: 18,
                    minWidth: 18
                  }
                }}
              >
                <Work sx={{ fontSize: 20, color: 'text.secondary' }} />
              </Badge>

              <Typography
                variant="body2"
                sx={{
                  fontWeight: 500,
                  fontSize: { xs: '0.875rem', md: '0.8rem' },
                  display: { xs: 'block', md: hasActiveJobs ? 'block' : 'none' } // Hide text on desktop when no active jobs
                }}
              >
                {hasActiveJobs ? `${activeJobs.length} active` :
                 jobs.length > 0 ? `${jobs.length} job${jobs.length !== 1 ? 's' : ''}` : 'No jobs'}
              </Typography>

              {hasActiveJobs && (
                <CircularProgress size={16} thickness={4} />
              )}
            </Box>

            <KeyboardArrowUp sx={{ fontSize: 20, color: 'text.secondary' }} />
          </Box>
        )}

        {/* Expanded Content */}
        {!isCollapsed && (
          <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            {/* Header */}
            <Box
              sx={{
                p: 2,
                borderBottom: 1,
                borderColor: 'divider',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                backgroundColor: 'grey.50',
                minHeight: 64,
                flexShrink: 0
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Work sx={{ fontSize: 20, color: 'text.secondary' }} />
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 500,
                    fontSize: '1.1rem',
                    color: 'text.primary'
                  }}
                >
                  Jobs
                </Typography>
                {hasActiveJobs && (
                  <Chip
                    label={`${activeJobs.length} active`}
                    size="small"
                    color="primary"
                    sx={{
                      height: 20,
                      fontSize: '0.7rem',
                      '& .MuiChip-label': {
                        px: 1
                      }
                    }}
                  />
                )}
              </Box>

              <IconButton
                onClick={onToggleCollapse}
                size="small"
                sx={{
                  color: 'text.secondary',
                  '&:hover': {
                    backgroundColor: 'action.hover'
                  }
                }}
              >
                <KeyboardArrowDown />
              </IconButton>
            </Box>

            {/* Content Area */}
            <Box sx={{ 
              flex: 1, 
              overflow: 'auto',
              '&::-webkit-scrollbar': {
                width: 6
              },
              '&::-webkit-scrollbar-track': {
                backgroundColor: 'transparent'
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: 'action.disabled',
                borderRadius: 1
              }
            }}>
              {jobs.length === 0 ? (
                <Box sx={{ p: 4, textAlign: 'center' }}>
                  <Work sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    No compression jobs yet
                  </Typography>
                  <Typography variant="caption" color="text.disabled">
                    Start compressing media to see jobs here
                  </Typography>
                </Box>
              ) : (
                <>
                  {/* Header with Clear All button */}
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'space-between', 
                    p: 2, 
                    pb: 1,
                    position: 'sticky',
                    top: 0,
                    backgroundColor: 'background.paper',
                    zIndex: 1
                  }}>
                    <Typography variant="subtitle2" sx={{ 
                      fontSize: '0.8rem', 
                      fontWeight: 600, 
                      color: 'text.secondary' 
                    }}>
                      All Jobs
                    </Typography>
                    <Button
                      onClick={onClearAllJobs}
                      size="small"
                      variant="outlined"
                      sx={{
                        fontSize: '0.65rem',
                        minWidth: 'auto',
                        px: 1,
                        py: 0.25,
                        height: 'auto'
                      }}
                    >
                      Clear All
                    </Button>
                  </Box>

                  {/* Deletion Policy Notice */}
                  <Box sx={{ px: 2, pb: 1 }}>
                    <Typography
                      variant="caption"
                      sx={{
                        fontSize: '0.5rem',
                        color: 'text.disabled',
                        fontStyle: 'italic',
                        display: 'block',
                        lineHeight: 1.3
                      }}
                    >
                      Jobs are deleted after 24 hours
                    </Typography>
                  </Box>

                  {/* Individual Job Items */}
                  <List sx={{ p: 0 }}>
                    {sortedJobs.map((job, index) => (
                      <React.Fragment key={job.jobId}>
                        <JobItem
                          job={job}
                          mediaItem={getMediaItemForJob(job)}
                          token={token}
                          compact={false}
                          onClearJob={onClearJob}
                        />
                        {index < sortedJobs.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                </>
              )}
            </Box>
          </Box>
        )}
      </Paper>
    </>
  );
};

export default JobsTray;
